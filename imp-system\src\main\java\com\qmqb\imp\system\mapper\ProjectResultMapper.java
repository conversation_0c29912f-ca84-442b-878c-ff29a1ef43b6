package com.qmqb.imp.system.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qmqb.imp.common.core.mapper.BaseMapperPlus;
import com.qmqb.imp.system.domain.ProjectResult;
import com.qmqb.imp.system.domain.vo.ProjectResultVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 项目成果表Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-13
 */
public interface ProjectResultMapper extends BaseMapperPlus<ProjectResultMapper, ProjectResult, ProjectResultVo> {

    /**
     * 分页查询项目成果列表（关联业务类型表）
     * @param page 分页对象
     * @param queryWrapper 查询条件
     * @param businessCategoryMajor 业务大类
     * @param businessCategoryMinor 业务小类
     * @return 项目成果列表
     */
    Page<ProjectResultVo> selectVoPageWithBusinessType(Page<ProjectResult> page,
                                                      @Param(Constants.WRAPPER) Wrapper<ProjectResult> queryWrapper,
                                                      @Param("businessCategoryMajor") String businessCategoryMajor,
                                                      @Param("businessCategoryMinor") String businessCategoryMinor);

    /**
     * 查询项目成果列表（关联业务类型表）
     * @param queryWrapper 查询条件
     * @param businessCategoryMajor 业务大类
     * @param businessCategoryMinor 业务小类
     * @return 项目成果列表
     */
    List<ProjectResultVo> selectVoListWithBusinessType(@Param(Constants.WRAPPER) Wrapper<ProjectResult> queryWrapper,
                                                       @Param("businessCategoryMajor") String businessCategoryMajor,
                                                       @Param("businessCategoryMinor") String businessCategoryMinor);

    /**
     * 根据ID查询项目成果详情（关联业务类型表）
     * @param id 项目成果ID
     * @return 项目成果详情
     */
    ProjectResultVo selectVoByIdWithBusinessType(@Param("id") Long id);
}
