package com.qmqb.imp.system.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qmqb.imp.system.domain.StoryResult;
import com.qmqb.imp.system.domain.bo.StoryResultBo;
import com.qmqb.imp.system.domain.vo.StoryResultVo;
import com.qmqb.imp.common.core.mapper.BaseMapperPlus;
import org.apache.ibatis.annotations.Param;

/**
 * 需求成果Mapper接口
 *
 * <AUTHOR>
 * @date 2025-08-13
 */
public interface StoryResultMapper extends BaseMapperPlus<StoryResultMapper, StoryResult, StoryResultVo> {

    /**
     * 分页查询需求成果列表
     * @param build
     * @param request
     * @return
     */
    Page<StoryResultVo> pageList(@Param("build") Page<Object> build, @Param("request") StoryResultBo request);
}
