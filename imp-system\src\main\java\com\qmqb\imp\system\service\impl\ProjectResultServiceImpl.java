package com.qmqb.imp.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.hzed.structure.common.exception.ServiceException;
import com.qmqb.imp.common.core.domain.PageQuery;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.common.utils.StringUtils;
import com.qmqb.imp.system.domain.ProjectResult;
import com.qmqb.imp.system.domain.bo.ProjectResultBo;
import com.qmqb.imp.system.domain.vo.ProjectResultVo;
import com.qmqb.imp.system.mapper.ProjectResultMapper;
import com.qmqb.imp.system.service.IProjectResultService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * 项目成果表Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-13
 */
@RequiredArgsConstructor
@Service
public class ProjectResultServiceImpl implements IProjectResultService {

    private final ProjectResultMapper baseMapper;

    /**
     * 查询项目成果表
     */
    @Override
    public ProjectResultVo queryById(Long id) {
        ProjectResultVo vo = baseMapper.selectVoByIdWithBusinessType(id);
        return vo;
    }

    /**
     * 查询项目成果表列表
     */
    @Override
    public TableDataInfo<ProjectResultVo> queryPageList(ProjectResultBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ProjectResult> lqw = buildQueryWrapper(bo);

        // 构建分页对象
        Page<ProjectResult> page = pageQuery.build();

        // 处理排序逻辑
        handleCustomSort(page, pageQuery);

        Page<ProjectResultVo> result = baseMapper.selectVoPageWithBusinessType(page, lqw,
                                                                               bo.getBusinessCategoryMajor(),
                                                                               bo.getBusinessCategoryMinor());

        return TableDataInfo.build(result);
    }

    /**
     * 查询项目成果表列表
     */
    @Override
    public List<ProjectResultVo> queryList(ProjectResultBo bo) {
        LambdaQueryWrapper<ProjectResult> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoListWithBusinessType(lqw,
                                                       bo.getBusinessCategoryMajor(),
                                                       bo.getBusinessCategoryMinor());
    }

    private LambdaQueryWrapper<ProjectResult> buildQueryWrapper(ProjectResultBo bo) {
        LambdaQueryWrapper<ProjectResult> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getBusinessTypeId() != null, ProjectResult::getBusinessTypeId, bo.getBusinessTypeId());
        lqw.eq(StringUtils.isNotBlank(bo.getResultType()), ProjectResult::getResultType, bo.getResultType());
        lqw.like(StringUtils.isNotBlank(bo.getProjectTaskName()), ProjectResult::getProjectTaskName, bo.getProjectTaskName());
        lqw.eq(StringUtils.isNotBlank(bo.getPriorityLevel()), ProjectResult::getPriorityLevel, bo.getPriorityLevel());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), ProjectResult::getStatus, bo.getStatus());

        lqw.like(StringUtils.isNotBlank(bo.getProjectManagers()), ProjectResult::getProjectManagers, bo.getProjectManagers());

        // 如果明确指定了归档标志，则按指定条件查询；否则默认只查询未归档的数据（archiveFlag != 1）
        if (bo.getArchiveFlag() != null) {
            lqw.eq(ProjectResult::getArchiveFlag, bo.getArchiveFlag());
        } else {
            // 默认不查询已归档的数据
            lqw.eq(ProjectResult::getArchiveFlag, 0);
        }

        return lqw;
    }

    /**
     * 新增项目成果表
     */
    @Override
    public Boolean insertByBo(ProjectResultBo bo) {
        ProjectResult add = BeanUtil.toBean(bo, ProjectResult.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改项目成果表
     */
    @Override
    public Boolean updateByBo(ProjectResultBo bo) {
        ProjectResult update = BeanUtil.toBean(bo, ProjectResult.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ProjectResult entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除项目成果表
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 归档项目成果
     */
    @Override
    public Boolean archiveById(Long id) {
        // 先查询当前记录
        ProjectResult projectResult = baseMapper.selectById(id);
        if (projectResult == null) {
            throw new ServiceException("项目成果不存在");
        }

        // 检查是否已经归档
        if (projectResult.getArchiveFlag() != null && projectResult.getArchiveFlag() == 1) {
            throw new ServiceException("该项目成果已经归档，无法重复归档");
        }

        // 更新归档标志
        ProjectResult update = new ProjectResult();
        update.setId(id);
        update.setArchiveFlag(1);

        return baseMapper.updateById(update) > 0;
    }

    /**
     * 处理自定义排序逻辑
     * 1. 默认按照业务类型的sort字段从小到大排序，然后再按照优先级P1-P2-P3进行排序
     * 2. 支持排序项：优先级、更新时间、完成时间、创建时间
     * 3. 对某一个排序项进行选择排序后，则不需按照默认排序，根据选择的排序项对整个表格进行排序
     */
    private void handleCustomSort(Page<ProjectResult> page, PageQuery pageQuery) {
        // 清除现有的排序
        page.orders().clear();

        // 如果有指定排序字段，则使用指定排序
        if (StringUtils.isNotBlank(pageQuery.getOrderByColumn())) {
            List<OrderItem> orderItems = new ArrayList<>();

            String[] orderByColumns = pageQuery.getOrderByColumn().split(",");
            String[] isAscArray = pageQuery.getIsAsc().split(",");

            for (int i = 0; i < orderByColumns.length; i++) {
                String column = orderByColumns[i].trim();
                boolean isAsc = i < isAscArray.length ? "asc".equalsIgnoreCase(isAscArray[i].trim()) : true;

                // 处理优先级的自定义排序
                if ("priority_level".equals(column) || "priorityLevel".equals(column)) {
                    String customOrder = "CASE pr.priority_level WHEN 'P1' THEN 1 WHEN 'P2' THEN 2 WHEN 'P3' THEN 3 ELSE 4 END";
                    orderItems.add(isAsc ? OrderItem.asc(customOrder) : OrderItem.desc(customOrder));
                } else {
                    // 转换为数据库字段名，并添加表别名
                    String dbColumn = StringUtils.toUnderScoreCase(column);
                    // 为主表字段添加pr.前缀
                    if ("updated_time".equals(dbColumn) || "completion_time".equals(dbColumn) || "created_time".equals(dbColumn)) {
                        dbColumn = "pr." + dbColumn;
                    }
                    orderItems.add(isAsc ? OrderItem.asc(dbColumn) : OrderItem.desc(dbColumn));
                }
            }

            page.addOrder(orderItems);
        } else {
            // 默认排序：先按业务类型的sort字段排序，再按优先级P1-P2-P3排序
            List<OrderItem> defaultOrders = new ArrayList<>();

            // 按业务类型的sort字段排序（从小到大）
            defaultOrders.add(OrderItem.asc("bt.sort"));

            // 按优先级P1-P2-P3排序
            String priorityOrder = "CASE pr.priority_level WHEN 'P1' THEN 1 WHEN 'P2' THEN 2 WHEN 'P3' THEN 3 ELSE 4 END";
            defaultOrders.add(OrderItem.asc(priorityOrder));

            page.addOrder(defaultOrders);
        }
    }
}
