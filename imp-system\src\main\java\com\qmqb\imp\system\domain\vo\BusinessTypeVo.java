package com.qmqb.imp.system.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 业务类型视图对象 tb_business_type
 *
 * <AUTHOR>
 * @date 2025-01-13
 */
@Data
@ExcelIgnoreUnannotated
public class BusinessTypeVo {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @ExcelProperty(value = "主键id")
    private Long id;

    /**
     * 业务类型名称
     */
    @ExcelProperty(value = "业务类型名称")
    private String businessTypeName;

    /**
     * 所属业务大类：1国内、2海外
     */
    @ExcelProperty(value = "所属业务大类")
    private String businessCategoryMajor;

    /**
     * 所属业务小类：1风控、2营销、3资金、4资产、5贷后、6自营业务、7综合、8其它
     */
    @ExcelProperty(value = "所属业务小类")
    private String businessCategoryMinor;

    /**
     * 排序
     */
    @ExcelProperty(value = "排序")
    private Integer sort;

    /**
     * 业务负责人
     */
    @ExcelProperty(value = "业务负责人")
    private String businessManager;

    /**
     * 创建人
     */
    @ExcelProperty(value = "创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新人
     */
    @ExcelProperty(value = "更新人")
    private String updateBy;

    /**
     * 更新时间
     */
    @ExcelProperty(value = "更新时间")
    private Date updateTime;

}
