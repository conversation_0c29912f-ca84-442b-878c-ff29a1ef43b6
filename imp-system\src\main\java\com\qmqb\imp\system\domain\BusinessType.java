package com.qmqb.imp.system.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.qmqb.imp.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 业务类型对象 tb_business_type
 *
 * <AUTHOR>
 * @date 2025-01-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("tb_business_type")
public class BusinessType extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 业务类型名称
     */
    private String businessTypeName;

    /**
     * 所属业务大类：1国内、2海外
     */
    private String businessCategoryMajor;

    /**
     * 所属业务小类：1风控、2营销、3资金、4资产、5贷后、6自营业务、7综合、8其它
     */
    private String businessCategoryMinor;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 业务负责人
     */
    private String businessManager;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private Integer delFlag;

}
