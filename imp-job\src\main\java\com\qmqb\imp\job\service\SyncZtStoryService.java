package com.qmqb.imp.job.service;

import com.alibaba.excel.util.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.hzed.structure.log.annotation.TraceId;
import com.qmqb.imp.common.constant.CommConstants;
import com.qmqb.imp.system.domain.StoryResult;
import com.qmqb.imp.system.domain.vo.StoryVO;
import com.qmqb.imp.system.mapper.StoryResultMapper;
import com.qmqb.imp.system.service.IStoryService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025-08-13 09:42
 */

@Slf4j
@Service
@RequiredArgsConstructor
public class SyncZtStoryService {

    private final IStoryService storyService;

    private final StoryResultMapper storyResultMapper;

    @TraceId("同步禅道需求")
    @XxlJob("syncZtStoryHandler")
    public ReturnT<String> syncZtStoryHandler(String param) {
        try {
            XxlJobLogger.log("开始执行同步禅道需求...");
            log.info("开始执行同步禅道需求...");
            //当参数是1时,数据全量更新，否则只更新修改或新增的数据
            boolean fullSync = StringUtils.isNotBlank(param) && param.equals(CommConstants.CommonValStr.ONE);
            // 获取同步开始时间（所有记录使用同一时间）
            Date syncTime = new Date();

            List<StoryVO> stories = fullSync ? storyService.getStoryListByLastUpdateTime(null) : getChangedStories();
            if (CollectionUtils.isEmpty(stories)) {
                log.info("没有需要同步的需求数据");
                return ReturnT.SUCCESS;
            }
            syncStories(stories, syncTime);
            XxlJobLogger.log("同步禅道需求任务完成");
            log.info("同步禅道需求任务完成");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("任务统计定时任务执行异常", e);
            return ReturnT.FAIL;
        }
    }

    private List<StoryVO> getChangedStories() {
        Date lastSyncTime = Optional.ofNullable(
            storyResultMapper.selectOne(new LambdaQueryWrapper<StoryResult>()
                    .select(StoryResult::getUpdateTime)
                    .orderByDesc(StoryResult::getUpdateTime)
                    .last("LIMIT 1"))
            ).map(StoryResult::getUpdateTime).orElse(null);
        XxlJobLogger.log("增量同步，上次同步时间: {}", lastSyncTime);
        return storyService.getStoryListByLastUpdateTime(lastSyncTime);
    }


    @Transactional(rollbackFor = Exception.class)
    public void syncStories(List<StoryVO> storyList, Date syncTime) {
        // 分组处理已删除和未删除的需求
        Map<Boolean, List<StoryVO>> partitionedStories = storyList.stream()
            .collect(Collectors.partitioningBy(story -> CommConstants.CommonValStr.ONE.equals(story.getDeleted())));
        // 处理已删除的需求
        handleDeletedStories(partitionedStories.get(true));
        // 处理新增/更新的需求
        handleUpsertStories(partitionedStories.get(false), syncTime);
    }

    private void handleDeletedStories(List<StoryVO> deletedStories) {
        if (CollectionUtils.isEmpty(deletedStories)) {
            return;
        }
        List<Long> deletedIds = deletedStories.stream()
            .map(story -> story.getId().longValue())
            .collect(Collectors.toList());
        storyResultMapper.delete(new LambdaQueryWrapper<StoryResult>()
            .in(StoryResult::getId, deletedIds));
    }


    private void handleUpsertStories(List<StoryVO> activeStories, Date syncTime) {
        if (CollectionUtils.isEmpty(activeStories)) {
            return;
        }
        List<Long> activeStoryIds = activeStories.stream().map(story -> story.getId().longValue()).collect(Collectors.toList());
        // 获取现有数据
        Map<Long, StoryResult> existingResults = storyResultMapper.selectList(new LambdaQueryWrapper<StoryResult>()
                    .in(StoryResult::getId, activeStoryIds))
            .stream().collect(Collectors.toMap(StoryResult::getId, Function.identity()));
        // 分组处理
        List<StoryResult> toInsert = new ArrayList<>();
        List<StoryResult> toUpdate = new ArrayList<>();
        activeStories.forEach(story -> {
            StoryResult newResult = convertToStoryResult(story, syncTime);
            StoryResult existing = existingResults.getOrDefault(story.getId().longValue(),null);
            if (existing == null) {
                toInsert.add(newResult);
            } else if (!isSame(story, existing)) {
                toUpdate.add(newResult);
            }
        });
        // 批量操作
        if (!toInsert.isEmpty()) {
            storyResultMapper.insertBatch(toInsert);
        }
        if (!toUpdate.isEmpty()) {
            storyResultMapper.updateBatchById(toUpdate);
        }
    }


    private boolean isSame(StoryVO source, StoryResult target) {
        return equalsWithNullEmpty(source.getProduct().longValue(), target.getProductId())
            && equalsWithNullEmpty(source.getProductName(), target.getProductName())
            && equalsWithNullEmpty(source.getTitle(), target.getTitle())
            && equalsWithNullEmpty(source.getStatus(), target.getStatus())
            && equalsWithNullEmpty(source.getStage(), target.getStage())
            && equalsWithNullEmpty(source.getOpenedby(), target.getOpenedBy())
            && equalsWithNullEmpty(source.getOpeneddate(), target.getOpenedDate())
            && equalsWithNullEmpty(source.getAssignedto(), target.getAssignedTo())
            && equalsWithNullEmpty(source.getAssigneddate(), target.getAssignedDate())
            && equalsWithNullEmpty(source.getReviewer(), target.getReviewedBy())
            && equalsWithNullEmpty(source.getRevieweddate(), target.getReviewedDate())
            && equalsWithNullEmpty(source.getClosedby(), target.getClosedBy())
            && equalsWithNullEmpty(source.getCloseddate(), target.getClosedDate())
            && equalsWithNullEmpty(source.getClosedreason(), target.getClosedReason())
            && equalsWithNullEmpty(source.getBugCount(), target.getBugCount())
            && equalsWithNullEmpty(source.getCaseCount(), target.getCaseCount())
            && equalsWithNullEmpty(source.getTaskCount(), target.getTaskCount())
            && equalsWithNullEmpty(source.getReleaseCount(), target.getReleaseCount());
    }

    /**
     * 比较两个对象是否相等
     * @param a
     * @param b
     * @return 如果两者相等或都为null/空字符串则返回true
     */
    private boolean equalsWithNullEmpty(Object a, Object b) {
        if (a == b) {
            return true;
        }
        if (a == null || b == null) {
            // 处理字符串类型的null和空字符串比较
            if (a instanceof String || b instanceof String) {
                return (a == null && "".equals(b)) || (b == null && "".equals(a));
            }
            return false;
        }
        return a.equals(b);
    }

    private StoryResult convertToStoryResult(StoryVO story, Date syncTime) {
        StoryResult build = StoryResult.builder()
            .id(story.getId().longValue())
            .productId(story.getProduct().longValue())
            .productName(story.getProductName())
            .title(story.getTitle())
            .status(story.getStatus())
            .stage(story.getStage())
            .openedBy(story.getOpenedby())
            .openedDate(story.getOpeneddate())
            .assignedTo(story.getAssignedto())
            .assignedDate(story.getAssigneddate())
            .reviewedBy(story.getReviewer())
            .reviewedDate(story.getRevieweddate())
            .closedBy(story.getClosedby())
            .closedDate(story.getCloseddate())
            .closedReason(story.getClosedreason())
            .bugCount(story.getBugCount())
            .caseCount(story.getCaseCount())
            .taskCount(story.getTaskCount())
            .releaseCount(story.getReleaseCount())
            .delFlag(0)
            .build();
        build.setUpdateTime(syncTime);
        return build;
    }

}
